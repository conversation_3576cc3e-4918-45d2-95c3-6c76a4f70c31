<template>
  <button
    class="vk-button"
    :class="{
      'is-plain': plain,
      'is-round': round,
      'is-circle': circle,
      'is-disabled': disabled,
      [`vk-button--${type}`]: type,
      [`vk-button--${size}`]: size,
    }"
    :disabled="disabled"
  >
    <span>
      <slot></slot>
    </span>
  </button>
</template>

<script lang="ts" setup>
import type { ButtonProps } from './types'

defineProps<ButtonProps>()
</script>

<style lang="scss" scoped></style>
