<template>
  <div>
    <v-k-button ref="buttonRef" type="primary" plain disabled>button--{{ count }}</v-k-button>
    <a href="sss">kljlkljl</a>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import VKButton from './components/Button/Button.vue'
import type { ButtonInstance } from './components/Button/types'

const buttonRef = ref<ButtonInstance | null>(null)
const count = ref(0)

onMounted(() => {
  if (buttonRef.value) {
    console.log(buttonRef.value.ref)
  }
})
</script>

<style lang="scss" scoped></style>
