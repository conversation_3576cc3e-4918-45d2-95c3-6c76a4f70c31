<template>
  <div>
    <v-k-button ref="buttonRef" type="primary" plain disabled>button</v-k-button>
    <a href="sss">jkjk<PERSON><PERSON>jk</a>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import VKButton from './components/Button/Button.vue'
import type { ButtonInstance } from './components/Button/types'

const buttonRef = ref<ButtonInstance | null>(null)

onMounted(() => {
  if (buttonRef.value) {
    console.log(buttonRef.value.ref)
  }
})
</script>

<style lang="scss" scoped></style>
